-- <PERSON>reate function to send SMS maturity alerts using Twilio API
CREATE OR REPLACE FUNCTION public.send_maturity_sms_alerts()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    alert_days integer;
    sms_enabled boolean;
    target_date date;
    investment_record record;
    sms_count integer := 0;
    result_json json;
    twilio_account_sid text;
    twilio_auth_token text;
    twilio_phone_number text;
    http_response http_response;
    mobile_number text;
    formatted_number text;
    sms_message text;
BEGIN
    -- Get notification settings
    SELECT 
        ns.alert_days_before, 
        ns.sms_enabled,
        ns.twilio_account_sid,
        ns.twilio_auth_token,
        ns.twilio_phone_number
    INTO 
        alert_days, 
        sms_enabled,
        twilio_account_sid,
        twilio_auth_token,
        twilio_phone_number
    FROM notification_settings ns
    LIMIT 1;
   
    -- Check if SMS is enabled
    IF NOT sms_enabled THEN
        RETURN json_build_object(
            'success', false,
            'message', 'SMS notifications are disabled'
        );
    END IF;
    
    -- Check if Twilio credentials are available
    IF twilio_account_sid IS NULL OR twilio_auth_token IS NULL OR twilio_phone_number IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'message', 'Twilio configuration missing in database settings'
        );
    END IF;
   
    -- Calculate target maturity date
    target_date := CURRENT_DATE + INTERVAL '1 day' * COALESCE(alert_days, 7);
   
    -- Loop through investments maturing on target date
    FOR investment_record IN
        SELECT
            i.id as investment_id,
            i.scheme_name,
            i.scheme_code,
            i.amount,
            i.maturity_amount,
            i.maturity_date,
            i.interest_rate,
            -- Primary applicant details
            c.id as primary_client_id,
            c.first_name as primary_first_name,
            c.last_name as primary_last_name,
            c.mobile_number as primary_mobile,
            c.contact_person2 as primary_secondary_mobile,
            c.country_code as primary_country_code,
            -- Secondary applicant details
            c2.id as secondary_client_id,
            c2.first_name as secondary_first_name,
            c2.last_name as secondary_last_name,
            c2.mobile_number as secondary_mobile,
            c2.contact_person2 as secondary_secondary_mobile,
            c2.country_code as secondary_country_code
        FROM investments i
        JOIN clients c ON i.client_id = c.id
        LEFT JOIN clients c2 ON i.second_applicant_id = c2.id
        WHERE i.status = 'active'
        AND i.maturity_date = target_date
        AND c.is_deleted = false
        -- Check if we haven't already sent an alert today
        AND NOT EXISTS (
            SELECT 1 FROM alerts a 
            WHERE a.investment_id = i.id 
            AND a.alert_type = 'maturity_reminder'
            AND a.channel = 'sms'
            AND a.alert_date = CURRENT_DATE
        )
    LOOP
        -- Create short SMS message for free version
        sms_message := 'Investment Alert: Your investment in ' || investment_record.scheme_name || 
                      ' (₹' || investment_record.amount || ') is maturing on ' || investment_record.maturity_date || 
                      '. Maturity amount: ₹' || investment_record.maturity_amount || 
                      '. Contact us for options. - Investment Team';

        -- Send SMS to primary applicant mobile
        IF investment_record.primary_mobile IS NOT NULL AND investment_record.primary_country_code = '+91' THEN
            -- Format mobile number
            mobile_number := REGEXP_REPLACE(investment_record.primary_mobile, '[^0-9]', '', 'g');
            IF LENGTH(mobile_number) = 10 THEN
                formatted_number := '+91' || mobile_number;
            ELSE
                formatted_number := '+' || mobile_number;
            END IF;

            -- Send SMS via Twilio API
            SELECT * FROM http((
                'POST',
                'https://api.twilio.com/2010-04-01/Accounts/' || twilio_account_sid || '/Messages.json',
                ARRAY[
                    http_header('Authorization', 'Basic ' || encode(twilio_account_sid || ':' || twilio_auth_token, 'base64')),
                    http_header('Content-Type', 'application/x-www-form-urlencoded')
                ],
                'application/x-www-form-urlencoded',
                'To=' || formatted_number || '&From=' || twilio_phone_number || '&Body=' || sms_message
            )) INTO http_response;

            -- Log the alert
            INSERT INTO alerts (
                investment_id,
                client_id,
                alert_type,
                channel,
                status,
                message,
                alert_date
            ) VALUES (
                investment_record.investment_id,
                investment_record.primary_client_id,
                'maturity_reminder',
                'sms',
                CASE 
                    WHEN http_response.status >= 200 AND http_response.status < 300 THEN 'sent'
                    ELSE 'failed'
                END,
                CASE 
                    WHEN http_response.status >= 200 AND http_response.status < 300 THEN 'Maturity SMS sent to ' || formatted_number || ' (Primary)'
                    ELSE 'Failed to send SMS to primary: ' || http_response.content
                END,
                CURRENT_DATE
            );

            IF http_response.status >= 200 AND http_response.status < 300 THEN
                sms_count := sms_count + 1;
            END IF;
        END IF;

        -- Send SMS to primary applicant secondary contact
        IF investment_record.primary_secondary_mobile IS NOT NULL AND investment_record.primary_country_code = '+91' THEN
            -- Format mobile number
            mobile_number := REGEXP_REPLACE(investment_record.primary_secondary_mobile, '[^0-9]', '', 'g');
            IF LENGTH(mobile_number) = 10 THEN
                formatted_number := '+91' || mobile_number;
            ELSE
                formatted_number := '+' || mobile_number;
            END IF;

            -- Send SMS via Twilio API
            SELECT * FROM http((
                'POST',
                'https://api.twilio.com/2010-04-01/Accounts/' || twilio_account_sid || '/Messages.json',
                ARRAY[
                    http_header('Authorization', 'Basic ' || encode(twilio_account_sid || ':' || twilio_auth_token, 'base64')),
                    http_header('Content-Type', 'application/x-www-form-urlencoded')
                ],
                'application/x-www-form-urlencoded',
                'To=' || formatted_number || '&From=' || twilio_phone_number || '&Body=' || sms_message
            )) INTO http_response;

            -- Log the alert
            INSERT INTO alerts (
                investment_id,
                client_id,
                alert_type,
                channel,
                status,
                message,
                alert_date
            ) VALUES (
                investment_record.investment_id,
                investment_record.primary_client_id,
                'maturity_reminder',
                'sms',
                CASE 
                    WHEN http_response.status >= 200 AND http_response.status < 300 THEN 'sent'
                    ELSE 'failed'
                END,
                CASE 
                    WHEN http_response.status >= 200 AND http_response.status < 300 THEN 'Maturity SMS sent to ' || formatted_number || ' (Primary Secondary Contact)'
                    ELSE 'Failed to send SMS to primary secondary contact: ' || http_response.content
                END,
                CURRENT_DATE
            );

            IF http_response.status >= 200 AND http_response.status < 300 THEN
                sms_count := sms_count + 1;
            END IF;
        END IF;

        -- Send SMS to secondary applicant mobile (if exists)
        IF investment_record.secondary_mobile IS NOT NULL AND investment_record.secondary_country_code = '+91' THEN
            -- Format mobile number
            mobile_number := REGEXP_REPLACE(investment_record.secondary_mobile, '[^0-9]', '', 'g');
            IF LENGTH(mobile_number) = 10 THEN
                formatted_number := '+91' || mobile_number;
            ELSE
                formatted_number := '+' || mobile_number;
            END IF;

            -- Send SMS via Twilio API
            SELECT * FROM http((
                'POST',
                'https://api.twilio.com/2010-04-01/Accounts/' || twilio_account_sid || '/Messages.json',
                ARRAY[
                    http_header('Authorization', 'Basic ' || encode(twilio_account_sid || ':' || twilio_auth_token, 'base64')),
                    http_header('Content-Type', 'application/x-www-form-urlencoded')
                ],
                'application/x-www-form-urlencoded',
                'To=' || formatted_number || '&From=' || twilio_phone_number || '&Body=' || sms_message
            )) INTO http_response;

            -- Log the alert
            INSERT INTO alerts (
                investment_id,
                client_id,
                alert_type,
                channel,
                status,
                message,
                alert_date
            ) VALUES (
                investment_record.investment_id,
                investment_record.secondary_client_id,
                'maturity_reminder',
                'sms',
                CASE 
                    WHEN http_response.status >= 200 AND http_response.status < 300 THEN 'sent'
                    ELSE 'failed'
                END,
                CASE 
                    WHEN http_response.status >= 200 AND http_response.status < 300 THEN 'Maturity SMS sent to ' || formatted_number || ' (Secondary)'
                    ELSE 'Failed to send SMS to secondary: ' || http_response.content
                END,
                CURRENT_DATE
            );

            IF http_response.status >= 200 AND http_response.status < 300 THEN
                sms_count := sms_count + 1;
            END IF;
        END IF;

        -- Send SMS to secondary applicant secondary contact (if exists)
        IF investment_record.secondary_secondary_mobile IS NOT NULL AND investment_record.secondary_country_code = '+91' THEN
            -- Format mobile number
            mobile_number := REGEXP_REPLACE(investment_record.secondary_secondary_mobile, '[^0-9]', '', 'g');
            IF LENGTH(mobile_number) = 10 THEN
                formatted_number := '+91' || mobile_number;
            ELSE
                formatted_number := '+' || mobile_number;
            END IF;

            -- Send SMS via Twilio API
            SELECT * FROM http((
                'POST',
                'https://api.twilio.com/2010-04-01/Accounts/' || twilio_account_sid || '/Messages.json',
                ARRAY[
                    http_header('Authorization', 'Basic ' || encode(twilio_account_sid || ':' || twilio_auth_token, 'base64')),
                    http_header('Content-Type', 'application/x-www-form-urlencoded')
                ],
                'application/x-www-form-urlencoded',
                'To=' || formatted_number || '&From=' || twilio_phone_number || '&Body=' || sms_message
            )) INTO http_response;

            -- Log the alert
            INSERT INTO alerts (
                investment_id,
                client_id,
                alert_type,
                channel,
                status,
                message,
                alert_date
            ) VALUES (
                investment_record.investment_id,
                investment_record.secondary_client_id,
                'maturity_reminder',
                'sms',
                CASE 
                    WHEN http_response.status >= 200 AND http_response.status < 300 THEN 'sent'
                    ELSE 'failed'
                END,
                CASE 
                    WHEN http_response.status >= 200 AND http_response.status < 300 THEN 'Maturity SMS sent to ' || formatted_number || ' (Secondary Secondary Contact)'
                    ELSE 'Failed to send SMS to secondary secondary contact: ' || http_response.content
                END,
                CURRENT_DATE
            );

            IF http_response.status >= 200 AND http_response.status < 300 THEN
                sms_count := sms_count + 1;
            END IF;
        END IF;
    END LOOP;
   
    -- Return result
    result_json := json_build_object(
        'success', true,
        'message', 'Processed ' || sms_count || ' maturity SMS alerts',
        'target_date', target_date,
        'alert_days_before', alert_days,
        'sms_sent', sms_count
    );
   
    RETURN result_json;
END;
$$;
